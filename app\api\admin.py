from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from ..core.database import get_db
from ..core.auth import get_current_user
from ..models.user import User
from ..models.branch import Branch
from ..models.department import Department
from ..models.position import Position
from ..services.admin_service import AdminService
from ..schemas.admin import (
    SystemStatsResponse, UserStatsResponse, AdminUserCreate, AdminUserUpdate,
    AdminApplicationUpdate, UserSearchResponse, ApplicationSearchResponse,
    ActivityResponse, AdminDashboardResponse, UserDetailResponse,
    ApplicationDetailResponse, BulkUserAction, BulkApplicationAction,
    AdminActionResponse, RoleResponse, PermissionResponse, RoleCreateRequest,
    PermissionCreateRequest, RoleUpdateRequest, PermissionUpdateRequest
)

router = APIRouter(prefix="/admin", tags=["admin"])

@router.get("/dashboard", response_model=AdminDashboardResponse)
async def get_admin_dashboard(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get complete admin dashboard data"""
    admin_service = AdminService(db)
    
    system_stats = admin_service.get_system_stats(str(current_user.id))
    user_stats = admin_service.get_user_stats(str(current_user.id))
    recent_activities = admin_service.get_recent_activities(str(current_user.id), limit=20)
    
    return AdminDashboardResponse(
        system_stats=system_stats,
        user_stats=user_stats,
        recent_activities=recent_activities
    )

@router.get("/stats/system", response_model=SystemStatsResponse)
async def get_system_stats(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get system-wide statistics"""
    admin_service = AdminService(db)
    return admin_service.get_system_stats(str(current_user.id))

@router.get("/stats/users", response_model=UserStatsResponse)
async def get_user_stats(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get detailed user statistics"""
    admin_service = AdminService(db)
    return admin_service.get_user_stats(str(current_user.id))

@router.get("/stats")
async def get_admin_stats(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get admin statistics for dashboard (simplified endpoint for Flask admin)"""
    admin_service = AdminService(db)
    system_stats = admin_service.get_system_stats(str(current_user.id))

    return {
        "total_users": system_stats.total_users,
        "active_users": system_stats.active_users,
        "verified_users": system_stats.verified_users,
        "total_applications": system_stats.total_applications,
        "active_applications": system_stats.active_applications,
        "locked_users": system_stats.locked_users,
        "recent_registrations": system_stats.recent_registrations
    }

@router.get("/activities", response_model=ActivityResponse)
async def get_recent_activities(
    limit: int = Query(50, ge=1, le=100),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get recent system activities"""
    admin_service = AdminService(db)
    activities = admin_service.get_recent_activities(str(current_user.id), limit)
    return ActivityResponse(activities=activities)

# User Management Endpoints
@router.get("/users/search", response_model=UserSearchResponse)
async def search_users(
    q: Optional[str] = Query(None, description="Search query"),
    page: int = Query(1, ge=1),
    limit: int = Query(20, ge=1, le=100),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Search users with pagination"""
    admin_service = AdminService(db)
    skip = (page - 1) * limit
    
    users, total = admin_service.search_users(str(current_user.id), q or "", skip, limit)
    
    user_data = [{
        "id": str(user.id),
        "username": user.username,
        "email": user.email,
        "full_name": user.full_name,
        "is_active": user.is_active,
        "is_verified": user.is_verified,
        "is_superuser": user.is_superuser,
        "created_at": user.created_at.isoformat(),
        "last_login": user.last_login.isoformat() if user.last_login else None,
        "is_locked": user.is_locked()
    } for user in users]
    
    return UserSearchResponse(
        users=user_data,
        total=total,
        page=page,
        limit=limit
    )

@router.get("/users/{user_id}", response_model=UserDetailResponse)
async def get_user_detail(
    user_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get detailed user information"""
    admin_service = AdminService(db)
    admin_service.verify_admin_access(str(current_user.id))
    
    user = admin_service.user_service.get_user_by_id(user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={"error": "user_not_found", "detail": "User not found"}
        )
    
    return UserDetailResponse.from_orm(user)

@router.post("/users", response_model=UserDetailResponse)
async def create_user(
    user_data: AdminUserCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new user (admin only)"""
    admin_service = AdminService(db)
    user = admin_service.create_user_as_admin(str(current_user.id), user_data)
    return UserDetailResponse.from_orm(user)

@router.put("/users/{user_id}", response_model=UserDetailResponse)
async def update_user(
    user_id: str,
    user_data: AdminUserUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update user (admin only)"""
    admin_service = AdminService(db)
    user = admin_service.update_user_as_admin(str(current_user.id), user_id, user_data)
    return UserDetailResponse.from_orm(user)

@router.delete("/users/{user_id}", response_model=AdminActionResponse)
async def delete_user(
    user_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete user (admin only)"""
    admin_service = AdminService(db)
    success = admin_service.delete_user_as_admin(str(current_user.id), user_id)
    
    if success:
        return AdminActionResponse(
            success=True,
            message="User deleted successfully"
        )
    else:
        return AdminActionResponse(
            success=False,
            message="Failed to delete user"
        )

@router.post("/users/{user_id}/unlock", response_model=AdminActionResponse)
async def unlock_user(
    user_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Unlock user account"""
    admin_service = AdminService(db)
    success = admin_service.unlock_user_account(str(current_user.id), user_id)
    
    if success:
        return AdminActionResponse(
            success=True,
            message="User account unlocked successfully"
        )
    else:
        return AdminActionResponse(
            success=False,
            message="Failed to unlock user account"
        )

@router.post("/users/bulk-action", response_model=AdminActionResponse)
async def bulk_user_action(
    action_data: BulkUserAction,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Perform bulk actions on users"""
    admin_service = AdminService(db)
    admin_service.verify_admin_access(str(current_user.id))
    
    success_count = 0
    errors = []
    
    for user_id in action_data.user_ids:
        try:
            if action_data.action == "activate":
                admin_service.user_service.activate_user(user_id)
            elif action_data.action == "deactivate":
                admin_service.user_service.deactivate_user(user_id)
            elif action_data.action == "verify":
                admin_service.user_service.verify_user(user_id)
            elif action_data.action == "delete":
                admin_service.delete_user_as_admin(str(current_user.id), user_id)
            elif action_data.action == "unlock":
                admin_service.unlock_user_account(str(current_user.id), user_id)
            else:
                errors.append(f"Unknown action: {action_data.action}")
                continue
            
            success_count += 1
        except Exception as e:
            errors.append(f"Failed to {action_data.action} user {user_id}: {str(e)}")
    
    return AdminActionResponse(
        success=success_count > 0,
        message=f"Successfully performed {action_data.action} on {success_count} users",
        affected_count=success_count,
        errors=errors if errors else None
    )

# Application Management Endpoints
@router.get("/applications", response_model=ApplicationSearchResponse)
async def get_all_applications(
    page: int = Query(1, ge=1),
    limit: int = Query(20, ge=1, le=100),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get all applications (admin view)"""
    admin_service = AdminService(db)
    skip = (page - 1) * limit
    
    applications, total = admin_service.get_all_applications(str(current_user.id), skip, limit)
    
    app_data = [{
        "id": str(app.id),
        "name": app.name,
        "description": app.description,
        "client_id": app.client_id,
        "is_active": app.is_active,
        "created_at": app.created_at.isoformat(),
        "created_by": app.created_by
    } for app in applications]
    
    return ApplicationSearchResponse(
        applications=app_data,
        total=total,
        page=page,
        limit=limit
    )

@router.get("/applications/{app_id}", response_model=ApplicationDetailResponse)
async def get_application_detail(
    app_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get detailed application information"""
    admin_service = AdminService(db)
    admin_service.verify_admin_access(str(current_user.id))
    
    app = admin_service.app_service.get_application_by_id(app_id)
    if not app:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={"error": "application_not_found", "detail": "Application not found"}
        )
    
    return ApplicationDetailResponse.from_orm(app)

@router.put("/applications/{app_id}", response_model=ApplicationDetailResponse)
async def update_application(
    app_id: str,
    app_data: AdminApplicationUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update application (admin only)"""
    admin_service = AdminService(db)
    app = admin_service.update_application_as_admin(str(current_user.id), app_id, app_data)
    return ApplicationDetailResponse.from_orm(app)

@router.delete("/applications/{app_id}", response_model=AdminActionResponse)
async def delete_application(
    app_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete application (admin only)"""
    admin_service = AdminService(db)
    success = admin_service.delete_application_as_admin(str(current_user.id), app_id)
    
    if success:
        return AdminActionResponse(
            success=True,
            message="Application deleted successfully"
        )
    else:
        return AdminActionResponse(
            success=False,
            message="Failed to delete application"
        )

@router.post("/applications/bulk-action", response_model=AdminActionResponse)
async def bulk_application_action(
    action_data: BulkApplicationAction,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Perform bulk actions on applications"""
    admin_service = AdminService(db)
    admin_service.verify_admin_access(str(current_user.id))
    
    success_count = 0
    errors = []
    
    for app_id in action_data.application_ids:
        try:
            if action_data.action == "activate":
                admin_service.app_service.activate_application(app_id)
            elif action_data.action == "deactivate":
                admin_service.app_service.deactivate_application(app_id)
            elif action_data.action == "delete":
                admin_service.delete_application_as_admin(str(current_user.id), app_id)
            else:
                errors.append(f"Unknown action: {action_data.action}")
                continue
            
            success_count += 1
        except Exception as e:
            errors.append(f"Failed to {action_data.action} application {app_id}: {str(e)}")
    
    return AdminActionResponse(
        success=success_count > 0,
        message=f"Successfully performed {action_data.action} on {success_count} applications",
        affected_count=success_count,
        errors=errors if errors else None
    )

# Role Management Endpoints
@router.get("/roles", response_model=List[RoleResponse])
async def get_all_roles(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get all roles"""
    admin_service = AdminService(db)
    admin_service.verify_admin_access(str(current_user.id))

    roles = admin_service.get_all_roles()
    return [RoleResponse(
        id=str(role.id),
        role_name=role.role_name,
        description=role.description,
        permissions=[p.permission_name for p in role.permissions]
    ) for role in roles]

@router.post("/roles", response_model=RoleResponse)
async def create_role(
    role_data: RoleCreateRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new role"""
    admin_service = AdminService(db)
    admin_service.verify_admin_access(str(current_user.id))

    role = admin_service.create_role(role_data.role_name, role_data.description, role_data.permissions)
    return RoleResponse(
        id=str(role.id),
        role_name=role.role_name,
        description=role.description,
        permissions=[p.permission_name for p in role.permissions]
    )

@router.put("/roles/{role_id}", response_model=RoleResponse)
async def update_role(
    role_id: str,
    role_data: RoleUpdateRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update a role"""
    admin_service = AdminService(db)
    admin_service.verify_admin_access(str(current_user.id))

    role = admin_service.update_role(role_id, role_data.dict(exclude_unset=True))
    if not role:
        raise HTTPException(status_code=404, detail="Role not found")

    return RoleResponse(
        id=str(role.id),
        role_name=role.role_name,
        description=role.description,
        permissions=[p.permission_name for p in role.permissions]
    )

@router.delete("/roles/{role_id}", response_model=AdminActionResponse)
async def delete_role(
    role_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete a role"""
    admin_service = AdminService(db)
    admin_service.verify_admin_access(str(current_user.id))

    success = admin_service.delete_role(role_id)
    return AdminActionResponse(
        success=success,
        message="Role deleted successfully" if success else "Failed to delete role"
    )

# Permission Management Endpoints
@router.get("/permissions", response_model=List[PermissionResponse])
async def get_all_permissions(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get all permissions"""
    admin_service = AdminService(db)
    admin_service.verify_admin_access(str(current_user.id))

    permissions = admin_service.get_all_permissions()
    return [PermissionResponse(
        id=str(perm.id),
        permission_name=perm.permission_name,
        description=perm.description
    ) for perm in permissions]

@router.post("/permissions", response_model=PermissionResponse)
async def create_permission(
    permission_data: PermissionCreateRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new permission"""
    admin_service = AdminService(db)
    admin_service.verify_admin_access(str(current_user.id))

    permission = admin_service.create_permission(
        permission_data.permission_name,
        permission_data.description
    )
    return PermissionResponse(
        id=str(permission.id),
        permission_name=permission.permission_name,
        description=permission.description
    )

@router.delete("/permissions/{permission_id}", response_model=AdminActionResponse)
async def delete_permission(
    permission_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete a permission"""
    admin_service = AdminService(db)
    admin_service.verify_admin_access(str(current_user.id))

    success = admin_service.delete_permission(permission_id)
    return AdminActionResponse(
        success=success,
        message="Permission deleted successfully" if success else "Failed to delete permission"
    )

# Organization Structure Endpoints
@router.get("/branches")
async def get_all_branches(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get all branches"""
    admin_service = AdminService(db)
    admin_service.verify_admin_access(str(current_user.id))

    branches = db.query(Branch).all()
    return [{"id": str(branch.id), "name": branch.branch_name, "code": branch.branch_code, "address": branch.address, "province": branch.province} for branch in branches]

@router.get("/departments")
async def get_all_departments(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get all departments"""
    admin_service = AdminService(db)
    admin_service.verify_admin_access(str(current_user.id))

    departments = db.query(Department).all()
    return [{"id": str(dept.id), "name": dept.department_name, "description": dept.description} for dept in departments]

@router.get("/positions")
async def get_all_positions(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get all positions"""
    admin_service = AdminService(db)
    admin_service.verify_admin_access(str(current_user.id))

    positions = db.query(Position).all()
    return [{"id": str(pos.id), "name": pos.position_name, "department_id": str(pos.department_id), "description": pos.description} for pos in positions]